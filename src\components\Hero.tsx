import React, { useEffect } from 'react';
import { ArrowRight, Download, Sparkles } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const Hero = () => {
  const { t, i18n } = useTranslation();

  // Charger la langue préférée au démarrage
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en'; // Valeur par défaut : 'en'
    i18n.changeLanguage(savedLanguage);

    // Appliquer la direction RTL si la langue est arabe
    document.documentElement.dir = savedLanguage === 'ar' ? 'rtl' : 'ltr';
  }, [i18n]);

  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center overflow-hidden bg-gradient-to-br from-white via-primary-50 to-accent-50 dark:from-primary-950 dark:via-primary-900 dark:to-secondary-950"
    >
      {/* Modern Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1)_0%,transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.2)_0%,transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(99,102,241,0.1)_0%,transparent_50%)] dark:bg-[radial-gradient(circle_at_70%_80%,rgba(99,102,241,0.2)_0%,transparent_50%)]"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary-500/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-accent-500/10 rounded-full blur-3xl animate-pulse-slow animation-delay-400"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-bounce-slow"></div>
      </div>

      <div className="relative z-10 container-custom py-20">
        <div className="max-w-5xl mx-auto text-center">
          {/* Professional Badge */}
          <div className="inline-flex items-center px-6 py-3 rounded-full glass border border-accent-200/50 dark:border-accent-700/50 text-primary-700 dark:text-primary-300 text-sm font-medium mb-12 animate-slide-down">
            <Sparkles className="w-4 h-4 mr-2 text-accent-500" />
            {t('hero.badge', { defaultValue: 'Available for new opportunities' })}
          </div>

          {/* Modern Heading */}
          <h1 className="text-4xl sm:text-6xl lg:text-7xl font-display font-bold text-primary-900 dark:text-white mb-8 animate-slide-up text-balance">
            <span className="block text-primary-600 dark:text-primary-400 text-2xl sm:text-3xl lg:text-4xl font-medium mb-4">
              {t('hero.greeting', { defaultValue: 'Hello, I\'m' })}
            </span>
            <span className="gradient-text">
              {t('hero.name')}
            </span>
          </h1>

          {/* Professional Subtitle */}
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-medium text-primary-700 dark:text-primary-300 mb-8 animate-slide-up animation-delay-200 text-balance">
            {t('hero.title')}
          </h2>

          {/* Clean Description */}
          <p className="text-lg sm:text-xl text-primary-600 dark:text-primary-400 mb-12 max-w-3xl mx-auto leading-relaxed animate-slide-up animation-delay-400 text-balance">
            {t('hero.description')}
          </p>

          {/* Modern CTA Buttons */}
          <div className="flex flex-col sm:flex-row justify-center gap-4 animate-slide-up animation-delay-600">
            <a
              href="#projects"
              className="btn-primary group"
            >
              <span>{t('hero.viewWork')}</span>
              <ArrowRight className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
            </a>
            <a
              href="#contact"
              className="btn-secondary group"
            >
              <span>{t('hero.contactMe')}</span>
              <Download className="ml-2 w-4 h-4 transition-transform group-hover:translate-y-1" />
            </a>
          </div>

          {/* Scroll Indicator */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
              <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
