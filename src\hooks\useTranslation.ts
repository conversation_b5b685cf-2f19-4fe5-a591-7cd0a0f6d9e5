import { useTranslation as useI18nTranslation } from 'react-i18next';
import { useEffect } from 'react';

export const useTranslation = () => {
  const { t, i18n } = useI18nTranslation();

  useEffect(() => {
    // Récupérer la langue sauvegardée ou utiliser la langue par défaut
    const savedLanguage = localStorage.getItem('language') || 'en';
    
    if (i18n.language !== savedLanguage) {
      i18n.changeLanguage(savedLanguage);
    }

    // Appliquer la direction RTL pour l'arabe
    const isRTL = savedLanguage === 'ar';
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = savedLanguage;
    
    // Ajouter une classe CSS pour le RTL
    if (isRTL) {
      document.documentElement.classList.add('rtl');
    } else {
      document.documentElement.classList.remove('rtl');
    }
  }, [i18n]);

  const changeLanguage = (language: string) => {
    i18n.changeLanguage(language);
    localStorage.setItem('language', language);
    
    // Appliquer immédiatement les changements de direction
    const isRTL = language === 'ar';
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
    
    if (isRTL) {
      document.documentElement.classList.add('rtl');
    } else {
      document.documentElement.classList.remove('rtl');
    }
  };

  return {
    t,
    i18n,
    changeLanguage,
    currentLanguage: i18n.language,
    isRTL: i18n.language === 'ar'
  };
};

export default useTranslation;
