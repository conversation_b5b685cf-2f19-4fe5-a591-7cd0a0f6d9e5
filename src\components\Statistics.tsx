import React, { useState, useEffect, useRef } from 'react';
import { Code, Users, Coffee, Award } from 'lucide-react';
import useTranslation from '../hooks/useTranslation';

const statisticsData = [
  {
    icon: <Code className="w-8 h-8" />,
    number: 15,
    suffix: '+',
    title: 'Projects Completed',
    description: 'Successful projects delivered',
    color: 'accent'
  },
  {
    icon: <Users className="w-8 h-8" />,
    number: 10,
    suffix: '+',
    title: 'Happy Clients',
    description: 'Satisfied customers worldwide',
    color: 'secondary'
  },
  {
    icon: <Coffee className="w-8 h-8" />,
    number: 500,
    suffix: '+',
    title: 'Cups of Coffee',
    description: 'Fuel for coding sessions',
    color: 'accent'
  },
  {
    icon: <Award className="w-8 h-8" />,
    number: 2,
    suffix: '+',
    title: 'Years Experience',
    description: 'In web development',
    color: 'secondary'
  }
];

const CountUpAnimation = ({ 
  end, 
  duration = 2000, 
  suffix = '', 
  isVisible 
}: { 
  end: number; 
  duration?: number; 
  suffix?: string; 
  isVisible: boolean;
}) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!isVisible) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);
      
      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [end, duration, isVisible]);

  return (
    <span className="text-4xl lg:text-5xl font-bold">
      {count}{suffix}
    </span>
  );
};

const Statistics = () => {
  const { t } = useTranslation();
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <section 
      ref={sectionRef}
      className="section-padding-sm bg-gradient-to-br from-accent-600 to-secondary-600 relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute top-0 left-0 w-full h-full bg-grid-pattern opacity-10"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-white/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container-custom relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-display font-bold text-white mb-4">
            {t('statistics.title', { defaultValue: 'Numbers That Matter' })}
          </h2>
          <div className="w-16 h-1 bg-white/30 mx-auto rounded-full mb-6"></div>
          <p className="text-lg text-white/80 max-w-2xl mx-auto text-balance">
            {t('statistics.subtitle', { defaultValue: 'Some key metrics that showcase my journey and achievements' })}
          </p>
        </div>

        {/* Statistics Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {statisticsData.map((stat, index) => (
            <div
              key={index}
              className="text-center group"
            >
              {/* Icon */}
              <div className="flex justify-center mb-6">
                <div className={`w-16 h-16 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-lg ${
                  stat.color === 'accent'
                    ? 'bg-white/20 hover:bg-white/30'
                    : 'bg-white/15 hover:bg-white/25'
                }`}>
                  <div className="text-white">
                    {stat.icon}
                  </div>
                </div>
              </div>

              {/* Number */}
              <div className="mb-4">
                <div className="text-white group-hover:scale-105 transition-transform duration-300">
                  <CountUpAnimation
                    end={stat.number}
                    suffix={stat.suffix}
                    isVisible={isVisible}
                  />
                </div>
              </div>

              {/* Title */}
              <h3 className="text-xl font-display font-bold text-white mb-2 group-hover:text-white/90 transition-colors duration-200">
                {t(`statistics.stat_${index + 1}_title`, { defaultValue: stat.title })}
              </h3>

              {/* Description */}
              <p className="text-white/70 text-sm">
                {t(`statistics.stat_${index + 1}_description`, { defaultValue: stat.description })}
              </p>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white">
            <Award className="w-5 h-5 mr-2" />
            <span className="text-sm font-medium">
              {t('statistics.commitment', { defaultValue: 'Committed to excellence in every project' })}
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Statistics;
