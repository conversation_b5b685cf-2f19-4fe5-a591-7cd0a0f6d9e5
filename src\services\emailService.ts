import emailjs from '@emailjs/browser';

// EmailJS configuration
const EMAILJS_SERVICE_ID = import.meta.env.VITE_EMAILJS_SERVICE_ID;
const EMAILJS_TEMPLATE_ID = import.meta.env.VITE_EMAILJS_TEMPLATE_ID;
const EMAILJS_PUBLIC_KEY = import.meta.env.VITE_EMAILJS_PUBLIC_KEY;

export interface ContactFormData {
  name: string;
  email: string;
  message: string;
}

export interface EmailResponse {
  success: boolean;
  message: string;
}

// Initialize EmailJS
if (EMAILJS_PUBLIC_KEY) {
  emailjs.init(EMAILJS_PUBLIC_KEY);
}

export const sendContactEmail = async (formData: ContactFormData): Promise<EmailResponse> => {
  // Check if EmailJS is configured
  if (!EMAILJS_SERVICE_ID || !EMAILJS_TEMPLATE_ID || !EMAILJS_PUBLIC_KEY) {
    console.warn('<PERSON>ail<PERSON><PERSON> not configured, using fallback method');
    return sendFallbackEmail(formData);
  }

  try {
    const templateParams = {
      from_name: formData.name,
      from_email: formData.email,
      message: formData.message,
      to_email: '<EMAIL>',
    };

    const response = await emailjs.send(
      EMAILJS_SERVICE_ID,
      EMAILJS_TEMPLATE_ID,
      templateParams
    );

    if (response.status === 200) {
      return {
        success: true,
        message: 'Message sent successfully! I will get back to you soon.',
      };
    } else {
      throw new Error('Failed to send email');
    }
  } catch (error) {
    console.error('EmailJS error:', error);
    return sendFallbackEmail(formData);
  }
};

// Fallback method when EmailJS is not available
const sendFallbackEmail = (formData: ContactFormData): EmailResponse => {
  // Create mailto link
  const subject = encodeURIComponent(`Portfolio Contact from ${formData.name}`);
  const body = encodeURIComponent(
    `Name: ${formData.name}\nEmail: ${formData.email}\n\nMessage:\n${formData.message}`
  );
  const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
  
  // Open email client
  window.open(mailtoLink, '_blank');
  
  return {
    success: true,
    message: 'Your email client has been opened. Please send the pre-filled email to complete your message.',
  };
};

// Alternative: Simple fetch to a serverless function (if you want to set one up later)
export const sendViaServerless = async (formData: ContactFormData): Promise<EmailResponse> => {
  try {
    const response = await fetch('/api/contact', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData),
    });

    if (response.ok) {
      return {
        success: true,
        message: 'Message sent successfully!',
      };
    } else {
      throw new Error('Server error');
    }
  } catch (error) {
    console.error('Serverless function error:', error);
    return sendFallbackEmail(formData);
  }
};
