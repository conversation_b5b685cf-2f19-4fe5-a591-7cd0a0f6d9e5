import React from 'react';
import { Gith<PERSON>, Linkedin, Mail, MapPin, Phone, Heart, ArrowUp } from 'lucide-react';
import useTranslation from '../hooks/useTranslation';

const Footer = () => {
  const { t, isRTL } = useTranslation();

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { key: 'home', href: '#home' },
    { key: 'about', href: '#about' },
    { key: 'services', href: '#services' },
    { key: 'projects', href: '#projects' },
    { key: 'skills', href: '#skills' },
    { key: 'experience', href: '#experience' },
    { key: 'contact', href: '#contact' }
  ];

  const socialLinks = [
    {
      name: 'GitHub',
      href: 'https://github.com/MohammedBetkaoui',
      icon: <Github className="w-5 h-5" />,
      color: 'hover:text-gray-400'
    },
    {
      name: 'LinkedIn',
      href: 'https://linkedin.com/in/mohammed-betkaoui',
      icon: <Linkedin className="w-5 h-5" />,
      color: 'hover:text-blue-400'
    },
    {
      name: 'Email',
      href: 'mailto:<EMAIL>',
      icon: <Mail className="w-5 h-5" />,
      color: 'hover:text-red-400'
    }
  ];

  return (
    <footer className="bg-primary-900 dark:bg-primary-950 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-accent-500 to-secondary-500"></div>
      </div>

      <div className="container-custom relative z-10">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-accent-600 to-secondary-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-xl">B</span>
                </div>
                <span className="text-2xl font-display font-bold">Betkaoui Mohammed</span>
              </div>
              
              <p className="text-primary-300 mb-6 leading-relaxed max-w-md">
                {t('footer.description', { 
                  defaultValue: 'Passionate web developer creating modern, scalable applications with cutting-edge technologies. Let\'s build something amazing together.' 
                })}
              </p>

              {/* Contact Info */}
              <div className="space-y-3">
                <div className="flex items-center text-primary-300">
                  <MapPin className="w-4 h-4 mr-3 text-accent-400" />
                  <span className="text-sm">
                    {t('footer.location', { defaultValue: 'Bordj Bou Arreridj, Algeria' })}
                  </span>
                </div>
                <div className="flex items-center text-primary-300">
                  <Mail className="w-4 h-4 mr-3 text-accent-400" />
                  <a 
                    href="mailto:<EMAIL>"
                    className="text-sm hover:text-accent-400 transition-colors duration-200"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center text-primary-300">
                  <Phone className="w-4 h-4 mr-3 text-accent-400" />
                  <span className="text-sm">
                    {t('footer.phone', { defaultValue: '+213 783 96 23 48' })}
                  </span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-display font-bold mb-6">
                {t('footer.quick_links', { defaultValue: 'Quick Links' })}
              </h3>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.key}>
                    <a
                      href={link.href}
                      className="text-primary-300 hover:text-accent-400 transition-colors duration-200 text-sm flex items-center group"
                    >
                      <span className={`w-1.5 h-1.5 bg-accent-500 rounded-full mr-3 group-hover:scale-125 transition-transform duration-200 ${
                        isRTL ? 'ml-3 mr-0' : ''
                      }`}></span>
                      {t(`nav.${link.key}`, { defaultValue: link.key.charAt(0).toUpperCase() + link.key.slice(1) })}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Services */}
            <div>
              <h3 className="text-lg font-display font-bold mb-6">
                {t('footer.services_title', { defaultValue: 'Services' })}
              </h3>
              <ul className="space-y-3">
                <li>
                  <span className="text-primary-300 text-sm flex items-center">
                    <span className={`w-1.5 h-1.5 bg-secondary-500 rounded-full mr-3 ${
                      isRTL ? 'ml-3 mr-0' : ''
                    }`}></span>
                    {t('footer.service_1', { defaultValue: 'Web Development' })}
                  </span>
                </li>
                <li>
                  <span className="text-primary-300 text-sm flex items-center">
                    <span className={`w-1.5 h-1.5 bg-secondary-500 rounded-full mr-3 ${
                      isRTL ? 'ml-3 mr-0' : ''
                    }`}></span>
                    {t('footer.service_2', { defaultValue: 'Mobile Development' })}
                  </span>
                </li>
                <li>
                  <span className="text-primary-300 text-sm flex items-center">
                    <span className={`w-1.5 h-1.5 bg-secondary-500 rounded-full mr-3 ${
                      isRTL ? 'ml-3 mr-0' : ''
                    }`}></span>
                    {t('footer.service_3', { defaultValue: 'Backend Development' })}
                  </span>
                </li>
                <li>
                  <span className="text-primary-300 text-sm flex items-center">
                    <span className={`w-1.5 h-1.5 bg-secondary-500 rounded-full mr-3 ${
                      isRTL ? 'ml-3 mr-0' : ''
                    }`}></span>
                    {t('footer.service_4', { defaultValue: 'Consulting' })}
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-primary-800 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <div className="text-primary-400 text-sm flex items-center">
              <span>© {currentYear} Betkaoui Mohammed. </span>
              <span className="mx-1">
                {t('footer.made_with', { defaultValue: 'Made with' })}
              </span>
              <Heart className="w-4 h-4 text-red-500 mx-1" />
              <span>
                {t('footer.in_algeria', { defaultValue: 'in Algeria' })}
              </span>
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-6">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`text-primary-400 ${social.color} transition-all duration-200 transform hover:scale-110`}
                  aria-label={social.name}
                >
                  {social.icon}
                </a>
              ))}
            </div>

            {/* Back to Top */}
            <button
              onClick={scrollToTop}
              className="flex items-center space-x-2 text-primary-400 hover:text-accent-400 transition-colors duration-200 group"
              aria-label="Back to top"
            >
              <span className="text-sm">
                {t('footer.back_to_top', { defaultValue: 'Back to top' })}
              </span>
              <ArrowUp className="w-4 h-4 group-hover:-translate-y-1 transition-transform duration-200" />
            </button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
