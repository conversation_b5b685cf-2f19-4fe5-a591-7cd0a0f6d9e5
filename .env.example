# EmailJS Configuration (Free email service)
# 1. Go to https://www.emailjs.com/
# 2. Create a free account
# 3. Set up a service (Gmail, Outlook, etc.)
# 4. Create an email template
# 5. Get your credentials from the dashboard
VITE_EMAILJS_SERVICE_ID=your_service_id
VITE_EMAILJS_TEMPLATE_ID=your_template_id
VITE_EMAILJS_PUBLIC_KEY=your_public_key

# Supabase Configuration (Optional - for admin features)
# Get these values from your Supabase project dashboard
VITE_SUPABASE_URL=https://your-project-url.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Development
VITE_APP_ENV=development
