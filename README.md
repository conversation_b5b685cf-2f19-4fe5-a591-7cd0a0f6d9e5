# 🚀 <PERSON><PERSON><PERSON>

Un portfolio moderne et responsive développé avec React, TypeScript, et Tailwind CSS.

## ✨ Fonctionnalités

- 🎨 **Design moderne** avec animations fluides
- 🌙 **Mode sombre/clair** 
- 🌍 **Multilingue** (Français, Anglais, Arabe)
- 📱 **Responsive** sur tous les appareils
- 📧 **Formulaire de contact** fonctionnel avec EmailJS
- ⚡ **Performance optimisée** avec Vite
- 🎯 **SEO friendly**

## 🛠️ Technologies

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Build**: Vite
- **Icons**: Lucide React
- **Email**: EmailJS (service gratuit)
- **Internationalisation**: react-i18next
- **Animations**: CSS Transitions & Transforms

## 🚀 Installation

1. **C<PERSON>r le projet**
```bash
git clone https://github.com/MohammedBetkaoui/portfolio.git
cd portfolio
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Configuration (Optionnel)**
```bash
cp .env.example .env
# Éditer .env avec vos credentials EmailJS
```

4. **Lancer le serveur de développement**
```bash
npm run dev
```

5. **Ouvrir dans le navigateur**
```
http://localhost:5173
```

## 📧 Configuration du formulaire de contact

Le portfolio utilise EmailJS pour le formulaire de contact. Voir [EMAILJS_SETUP.md](./EMAILJS_SETUP.md) pour la configuration complète.

### Configuration rapide :
1. Créer un compte sur [EmailJS](https://www.emailjs.com/)
2. Configurer un service email
3. Créer un template
4. Mettre à jour le fichier `.env`

**Note**: Si EmailJS n'est pas configuré, le formulaire ouvrira automatiquement votre client email par défaut.

## 🏗️ Build pour la production

```bash
npm run build
```

Les fichiers de production seront dans le dossier `dist/`.

## 📁 Structure du projet

```
src/
├── components/          # Composants React
│   ├── layout/         # Composants de mise en page
│   ├── navigation/     # Navigation
│   ├── Hero.tsx        # Section héro
│   ├── About.tsx       # Section à propos
│   ├── Projects.tsx    # Section projets
│   ├── Skills.tsx      # Section compétences
│   └── Contact.tsx     # Section contact
├── services/           # Services (EmailJS, etc.)
├── locales/           # Fichiers de traduction
├── context/           # Contextes React (thème, etc.)
└── styles/            # Styles CSS
```

## 🌍 Langues supportées

- 🇫🇷 Français
- 🇬🇧 Anglais  
- 🇩🇿 Arabe

## 🎨 Personnalisation

### Couleurs
Les couleurs sont définies dans `tailwind.config.js` :
- **Primary**: Bleu (pour les éléments principaux)
- **Secondary**: Gris (pour le texte et arrière-plans)
- **Accent**: Violet (pour les accents et highlights)

### Contenu
- **Projets**: Modifier `src/components/Projects.tsx`
- **Compétences**: Modifier `src/components/Skills.tsx`
- **Traductions**: Modifier les fichiers dans `src/locales/`

## 🔧 Dépannage

Voir [TROUBLESHOOTING.md](./TROUBLESHOOTING.md) pour les solutions aux problèmes courants.

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](./LICENSE) pour plus de détails.

## 📞 Contact

- **Email**: <EMAIL>
- **GitHub**: [@MohammedBetkaoui](https://github.com/MohammedBetkaoui)
- **LinkedIn**: [Mohammed Betkaoui](https://www.linkedin.com/in/mohammed-betkaoui-b005342a5/)

---

⭐ N'hésitez pas à donner une étoile si ce projet vous a aidé !
