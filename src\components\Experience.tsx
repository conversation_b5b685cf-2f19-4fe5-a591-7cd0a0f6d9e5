import React from 'react';
import { Calendar, MapPin, Award, GraduationCap, Briefcase } from 'lucide-react';
import useTranslation from '../hooks/useTranslation';

const experienceData = [
  {
    type: 'work',
    title: 'Full Stack Developer',
    company: 'Tech Solutions',
    location: 'Remote',
    period: '2023 - Present',
    description: 'Developing modern web applications using React, Laravel, and cloud technologies.',
    achievements: [
      'Built 5+ production applications',
      'Improved performance by 40%',
      'Led a team of 3 developers'
    ]
  },
  {
    type: 'work',
    title: 'Web Developer',
    company: 'Digital Agency',
    location: 'Bordj Bou Arreridj',
    period: '2022 - 2023',
    description: 'Created responsive websites and e-commerce platforms for various clients.',
    achievements: [
      'Delivered 10+ client projects',
      'Increased client satisfaction by 95%',
      'Specialized in Laravel & React'
    ]
  },
  {
    type: 'education',
    title: 'Master in Computer Science',
    company: 'University of Bordj Bou Arreridj',
    location: 'Bordj Bou Arreridj, Algeria',
    period: '2021 - 2023',
    description: 'Specialized in Software Engineering and Web Development.',
    achievements: [
      'Graduated with Honors',
      'Thesis on Modern Web Technologies',
      'Active in coding competitions'
    ]
  },
  {
    type: 'education',
    title: 'Bachelor in Computer Science',
    company: 'University of Bordj Bou Arreridj',
    location: 'Bordj Bou Arreridj, Algeria',
    period: '2018 - 2021',
    description: 'Foundation in computer science, programming, and software development.',
    achievements: [
      'Strong academic performance',
      'Participated in hackathons',
      'Built first web applications'
    ]
  }
];

const Experience = () => {
  const { t, isRTL } = useTranslation();

  return (
    <section id="experience" className="section-padding bg-white dark:bg-primary-950 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-accent-100 dark:bg-accent-900/20 rounded-full blur-3xl opacity-30"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-secondary-100 dark:bg-secondary-900/20 rounded-full blur-3xl opacity-20"></div>
      </div>

      <div className="container-custom relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          <h2 className="text-3xl lg:text-4xl font-display font-bold text-primary-900 dark:text-white mb-4">
            {t('experience.title', { defaultValue: 'Experience & Education' })}
          </h2>
          <div className="w-16 h-1 bg-gradient-to-r from-accent-500 to-secondary-500 mx-auto rounded-full mb-6"></div>
          <p className="text-lg text-primary-600 dark:text-primary-400 max-w-2xl mx-auto text-balance">
            {t('experience.subtitle', { defaultValue: 'My professional journey and educational background' })}
          </p>
        </div>

        {/* Timeline */}
        <div className="relative">
          {/* Timeline Line */}
          <div className={`absolute top-0 bottom-0 w-0.5 bg-gradient-to-b from-accent-500 to-secondary-500 ${
            isRTL ? 'right-8' : 'left-8'
          } md:left-1/2 md:right-auto transform md:-translate-x-0.5`}></div>

          {/* Timeline Items */}
          <div className="space-y-12">
            {experienceData.map((item, index) => (
              <div
                key={index}
                className={`relative flex items-center ${
                  index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                } ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}
              >
                {/* Timeline Dot */}
                <div className={`absolute ${
                  isRTL ? 'right-6' : 'left-6'
                } md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 rounded-full border-4 border-white dark:border-primary-950 z-10 ${
                  item.type === 'work' 
                    ? 'bg-accent-500' 
                    : 'bg-secondary-500'
                }`}></div>

                {/* Content Card */}
                <div className={`w-full md:w-5/12 ${
                  index % 2 === 0 ? 'md:pr-8' : 'md:pl-8'
                } ${isRTL ? 'pr-16' : 'pl-16'} md:pl-0 md:pr-0`}>
                  <div className="card card-hover p-6 group">
                    {/* Icon */}
                    <div className="flex items-center mb-4">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center mr-4 ${
                        item.type === 'work'
                          ? 'bg-accent-100 dark:bg-accent-900/30'
                          : 'bg-secondary-100 dark:bg-secondary-900/30'
                      }`}>
                        {item.type === 'work' ? (
                          <Briefcase className={`w-6 h-6 ${
                            item.type === 'work' ? 'text-accent-600' : 'text-secondary-600'
                          }`} />
                        ) : (
                          <GraduationCap className="w-6 h-6 text-secondary-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-display font-bold text-primary-900 dark:text-white group-hover:text-accent-600 dark:group-hover:text-accent-400 transition-colors duration-200">
                          {t(`experience.item_${index + 1}_title`, { defaultValue: item.title })}
                        </h3>
                        <p className="text-primary-600 dark:text-primary-400 font-medium">
                          {t(`experience.item_${index + 1}_company`, { defaultValue: item.company })}
                        </p>
                      </div>
                    </div>

                    {/* Meta Information */}
                    <div className="flex flex-wrap gap-4 mb-4 text-sm text-primary-500 dark:text-primary-400">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-2" />
                        {t(`experience.item_${index + 1}_period`, { defaultValue: item.period })}
                      </div>
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 mr-2" />
                        {t(`experience.item_${index + 1}_location`, { defaultValue: item.location })}
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-primary-600 dark:text-primary-400 mb-4 leading-relaxed">
                      {t(`experience.item_${index + 1}_description`, { defaultValue: item.description })}
                    </p>

                    {/* Achievements */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-semibold text-primary-700 dark:text-primary-300 flex items-center">
                        <Award className="w-4 h-4 mr-2" />
                        {t('experience.achievements', { defaultValue: 'Key Achievements' })}
                      </h4>
                      <ul className="space-y-1">
                        {item.achievements.map((achievement, achIndex) => (
                          <li
                            key={achIndex}
                            className="text-sm text-primary-600 dark:text-primary-400 flex items-start"
                          >
                            <span className="w-1.5 h-1.5 bg-accent-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                            {t(`experience.item_${index + 1}_achievement_${achIndex + 1}`, { defaultValue: achievement })}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Spacer for alternating layout */}
                <div className="hidden md:block w-5/12"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <a
            href="/cv.pdf"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-primary group"
          >
            <span>{t('experience.download_cv', { defaultValue: 'Download CV' })}</span>
            <Award className="ml-2 w-4 h-4 transition-transform group-hover:translate-y-1" />
          </a>
        </div>
      </div>
    </section>
  );
};

export default Experience;
