# 📧 Configuration EmailJS

## Pourquoi EmailJS ?

EmailJS est un service gratuit qui permet d'envoyer des emails directement depuis le frontend sans avoir besoin d'un serveur backend. C'est parfait pour les formulaires de contact de portfolio.

## 🚀 Configuration Rapide (5 minutes)

### Étape 1: Créer un compte EmailJS
1. Allez sur [https://www.emailjs.com/](https://www.emailjs.com/)
2. Cliquez sur "Sign Up" et créez un compte gratuit
3. Confirmez votre email

### Étape 2: Configurer un service email
1. Dans le dashboard, allez dans "Email Services"
2. Cliquez sur "Add New Service"
3. Choisissez votre fournisseur (Gmail, Outlook, Yahoo, etc.)
4. Suivez les instructions pour connecter votre email
5. Notez le **Service ID** généré

### Étape 3: Créer un template d'email
1. Allez dans "Email Templates"
2. C<PERSON><PERSON> sur "Create New Template"
3. Utilisez ce template de base :

```html
Subject: Portfolio Contact from {{from_name}}

From: {{from_name}} ({{from_email}})

Message:
{{message}}

---
Sent from your portfolio contact form
```

4. Notez le **Template ID** généré

### Étape 4: Obtenir la clé publique
1. Allez dans "Account" > "General"
2. Copiez votre **Public Key**

### Étape 5: Mettre à jour votre .env
```env
VITE_EMAILJS_SERVICE_ID=service_xxxxxxx
VITE_EMAILJS_TEMPLATE_ID=template_xxxxxxx
VITE_EMAILJS_PUBLIC_KEY=xxxxxxxxxxxxxxx
```

## 🎯 Template Variables

Votre template EmailJS peut utiliser ces variables :
- `{{from_name}}` - Nom de la personne qui envoie
- `{{from_email}}` - Email de la personne qui envoie  
- `{{message}}` - Message du formulaire
- `{{to_email}}` - Votre email (<EMAIL>)

## 🔧 Test

1. Redémarrez votre serveur de développement
2. Remplissez le formulaire de contact
3. Vérifiez votre boîte email

## 💡 Avantages

✅ **Gratuit** - 200 emails/mois gratuitement
✅ **Simple** - Pas de serveur backend nécessaire
✅ **Fiable** - Service établi et stable
✅ **Sécurisé** - Pas d'exposition de credentials sensibles
✅ **Fallback** - Si non configuré, ouvre le client email

## 🚨 Fallback

Si EmailJS n'est pas configuré, le formulaire ouvrira automatiquement le client email par défaut avec un message pré-rempli. L'utilisateur peut alors envoyer l'email manuellement.

## 📊 Limites du plan gratuit

- 200 emails par mois
- 2 services email
- Support communautaire

Pour un portfolio personnel, c'est largement suffisant !
