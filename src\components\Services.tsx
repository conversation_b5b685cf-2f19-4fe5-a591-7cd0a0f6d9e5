import React from 'react';
import { Code, Smartphone, Globe, Database, Zap, Shield } from 'lucide-react';
import useTranslation from '../hooks/useTranslation';

const servicesData = [
  {
    icon: <Globe className="w-8 h-8" />,
    title: 'Web Development',
    description: 'Custom web applications built with modern technologies like React, Laravel, and Node.js.',
    features: ['Responsive Design', 'SEO Optimized', 'Fast Performance', 'Cross-browser Compatible'],
    color: 'accent'
  },
  {
    icon: <Smartphone className="w-8 h-8" />,
    title: 'Mobile Development',
    description: 'Native and cross-platform mobile applications for iOS and Android.',
    features: ['React Native', 'Flutter', 'Native Performance', 'App Store Ready'],
    color: 'secondary'
  },
  {
    icon: <Database className="w-8 h-8" />,
    title: 'Backend Development',
    description: 'Robust server-side solutions with APIs, databases, and cloud integration.',
    features: ['RESTful APIs', 'Database Design', 'Cloud Deployment', 'Security'],
    color: 'accent'
  },
  {
    icon: <Code className="w-8 h-8" />,
    title: 'Full-Stack Solutions',
    description: 'Complete end-to-end development from concept to deployment.',
    features: ['Frontend + Backend', 'Database Integration', 'DevOps', 'Maintenance'],
    color: 'secondary'
  },
  {
    icon: <Zap className="w-8 h-8" />,
    title: 'Performance Optimization',
    description: 'Speed up your existing applications and improve user experience.',
    features: ['Code Optimization', 'Database Tuning', 'Caching', 'Monitoring'],
    color: 'accent'
  },
  {
    icon: <Shield className="w-8 h-8" />,
    title: 'Security & Maintenance',
    description: 'Keep your applications secure and up-to-date with regular maintenance.',
    features: ['Security Audits', 'Updates', 'Backup Solutions', '24/7 Support'],
    color: 'secondary'
  }
];

const Services = () => {
  const { t } = useTranslation();

  return (
    <section id="services" className="section-padding bg-primary-50/30 dark:bg-primary-900/30 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-20 w-72 h-72 bg-accent-100 dark:bg-accent-900/20 rounded-full blur-3xl opacity-30"></div>
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-secondary-100 dark:bg-secondary-900/20 rounded-full blur-3xl opacity-20"></div>
      </div>

      <div className="container-custom relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          <h2 className="text-3xl lg:text-4xl font-display font-bold text-primary-900 dark:text-white mb-4">
            {t('services.title', { defaultValue: 'Services I Offer' })}
          </h2>
          <div className="w-16 h-1 bg-gradient-to-r from-accent-500 to-secondary-500 mx-auto rounded-full mb-6"></div>
          <p className="text-lg text-primary-600 dark:text-primary-400 max-w-2xl mx-auto text-balance">
            {t('services.subtitle', { defaultValue: 'Comprehensive development services to bring your ideas to life' })}
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {servicesData.map((service, index) => (
            <div
              key={index}
              className="card card-hover card-interactive group p-8 text-center relative overflow-hidden"
            >
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300">
                <div className={`w-full h-full bg-gradient-to-br ${
                  service.color === 'accent' 
                    ? 'from-accent-500/20 to-accent-600/20' 
                    : 'from-secondary-500/20 to-secondary-600/20'
                }`}></div>
              </div>

              {/* Icon */}
              <div className="relative z-10 flex justify-center mb-6">
                <div className={`w-16 h-16 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-lg group-hover:shadow-xl ${
                  service.color === 'accent'
                    ? 'bg-gradient-to-br from-accent-500 to-accent-600'
                    : 'bg-gradient-to-br from-secondary-500 to-secondary-600'
                }`}>
                  <div className="text-white">
                    {service.icon}
                  </div>
                </div>
              </div>

              {/* Title */}
              <h3 className="relative z-10 text-xl font-display font-bold text-primary-900 dark:text-white mb-4 group-hover:text-accent-600 dark:group-hover:text-accent-400 transition-colors duration-200">
                {t(`services.service_${index + 1}_title`, { defaultValue: service.title })}
              </h3>

              {/* Description */}
              <p className="relative z-10 text-primary-600 dark:text-primary-400 mb-6 leading-relaxed">
                {t(`services.service_${index + 1}_description`, { defaultValue: service.description })}
              </p>

              {/* Features */}
              <div className="relative z-10 space-y-2">
                {service.features.map((feature, featureIndex) => (
                  <div
                    key={featureIndex}
                    className="flex items-center justify-center text-sm text-primary-600 dark:text-primary-400"
                  >
                    <span className={`w-1.5 h-1.5 rounded-full mr-3 ${
                      service.color === 'accent' ? 'bg-accent-500' : 'bg-secondary-500'
                    }`}></span>
                    {t(`services.service_${index + 1}_feature_${featureIndex + 1}`, { defaultValue: feature })}
                  </div>
                ))}
              </div>

              {/* Hover Effect */}
              <div className={`absolute bottom-0 left-0 right-0 h-1 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ${
                service.color === 'accent'
                  ? 'bg-gradient-to-r from-accent-500 to-accent-600'
                  : 'bg-gradient-to-r from-secondary-500 to-secondary-600'
              }`}></div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="max-w-2xl mx-auto mb-8">
            <h3 className="text-2xl font-display font-bold text-primary-900 dark:text-white mb-4">
              {t('services.cta_title', { defaultValue: 'Ready to Start Your Project?' })}
            </h3>
            <p className="text-lg text-primary-600 dark:text-primary-400">
              {t('services.cta_description', { defaultValue: 'Let\'s discuss your ideas and create something amazing together.' })}
            </p>
          </div>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <a
              href="#contact"
              className="btn-primary group"
            >
              <span>{t('services.get_quote', { defaultValue: 'Get a Quote' })}</span>
              <Zap className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
            </a>
            <a
              href="#projects"
              className="btn-secondary group"
            >
              <span>{t('services.view_portfolio', { defaultValue: 'View Portfolio' })}</span>
              <Code className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
