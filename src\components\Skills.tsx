import React, { useEffect } from 'react';
import { Code2, Database, Layout, Server } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const skillCategories = [
  {
    title: "Frontend Development",
    icon: <Layout className="w-8 h-8 text-blue-600 dark:text-blue-400" />,
    skills: ["React js"]
  },
  {
    title: "Backend Development",
    icon: <Server className="w-8 h-8 text-blue-600 dark:text-blue-400" />,
    skills: ["Laravel 11", "PHP 8", "RESTful APIs","Express js"]
  },
  {
    title: "Database",
    icon: <Database className="w-8 h-8 text-blue-600 dark:text-blue-400" />,
    skills: ["MySQL", "MongoDB"]
  },
  {
    title: "Programming Languages",
    icon: <Code2 className="w-8 h-8 text-blue-600 dark:text-blue-400" />,
    skills: ["PHP", "JavaScript", "Dart"]
  }
];

const Skills = () => {
  const { t, i18n } = useTranslation();

  // Charger la langue préférée depuis localStorage au démarrage
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en'; // Valeur par défaut : 'en'
    i18n.changeLanguage(savedLanguage);

    // Appliquer la direction RTL si la langue est arabe
    document.documentElement.dir = savedLanguage === 'ar' ? 'rtl' : 'ltr';
  }, [i18n]);

  return (
    <section id="skills" className="section-padding bg-primary-50/50 dark:bg-primary-900/50 relative overflow-hidden">
      {/* Modern Background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-accent-200/30 dark:bg-accent-800/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-secondary-200/30 dark:bg-secondary-800/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container-custom relative z-10">
        <div className="text-center mb-20">
          <h2 className="text-3xl lg:text-4xl font-display font-bold text-primary-900 dark:text-white mb-4">
            {t('skills.title')}
          </h2>
          <div className="w-16 h-1 bg-gradient-to-r from-accent-500 to-secondary-500 mx-auto rounded-full mb-6"></div>
          <p className="text-lg text-primary-600 dark:text-primary-400 max-w-2xl mx-auto text-balance">
            {t('skills.subtitle', { defaultValue: 'Technologies and tools I use to bring ideas to life.' })}
          </p>
        </div>

        {/* Modern Skills Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {skillCategories.map((category, index) => (
            <div key={index} className="card card-hover group p-8 text-center">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-accent-500 to-secondary-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-all duration-300 shadow-lg group-hover:shadow-xl">
                  <div className="text-white">
                    {category.icon}
                  </div>
                </div>
              </div>

              <h3 className="text-xl font-display font-bold text-primary-900 dark:text-white mb-6 group-hover:text-accent-600 dark:group-hover:text-accent-400 transition-colors duration-200">
                {t(`skills.category_${index + 1}_title`, { defaultValue: category.title })}
              </h3>

              {/* Modern Skill Pills */}
              <div className="flex flex-wrap gap-2 justify-center">
                {category.skills.map((skill, skillIndex) => (
                  <span
                    key={skillIndex}
                    className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-800 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-700 hover:bg-accent-100 dark:hover:bg-accent-900/30 hover:text-accent-700 dark:hover:text-accent-300 hover:border-accent-300 dark:hover:border-accent-600 transition-all duration-200 cursor-default"
                  >
                    {t(`skills.skill_${index + 1}_${skillIndex + 1}`, { defaultValue: skill })}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Additional Skills Section */}
        <div className="mt-20 text-center">
          <h3 className="text-2xl font-display font-bold text-primary-900 dark:text-white mb-8">
            {t('skills.additional_title', { defaultValue: 'Additional Skills' })}
          </h3>
          <div className="flex flex-wrap justify-center gap-3 max-w-4xl mx-auto">
            {['Git', 'Docker', 'Linux', 'VS Code', 'Figma', 'Postman', 'Tailwind CSS', 'Bootstrap'].map((skill, index) => (
              <span
                key={index}
                className="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium bg-white dark:bg-primary-800 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-700 hover:shadow-md transition-all duration-200 cursor-default"
              >
                {skill}
              </span>
            ))}
          </div>
        </div>

        {/* Stats Section */}
        <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">2+</div>
            <div className="text-gray-600 dark:text-gray-400">Years Experience</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-accent-600 dark:text-accent-400 mb-2">10+</div>
            <div className="text-gray-600 dark:text-gray-400">Projects Completed</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">5+</div>
            <div className="text-gray-600 dark:text-gray-400">Technologies</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-accent-600 dark:text-accent-400 mb-2">100%</div>
            <div className="text-gray-600 dark:text-gray-400">Client Satisfaction</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
