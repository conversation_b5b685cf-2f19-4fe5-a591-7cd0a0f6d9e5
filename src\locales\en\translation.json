{"hero": {"name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Web Developer", "description": "Crafting intuitive and high-performance applications with Laravel & React js", "viewWork": "View My Work", "contactMe": "Contact Me"}, "about": {"title": "About Me", "intro": "I'm a passionate Web Developer with extensive experience in building modern, scalable applications. Specializing in Laravel and React js, I focus on creating intuitive user experiences backed by robust server-side architecture.", "details": "With a strong foundation in both frontend and backend development, I bring ideas to life through clean code and innovative solutions. I'm constantly learning and adapting to new technologies to deliver the best possible results.", "frontend": "Frontend", "frontendSkills": "React js", "backend": "Backend", "backendSkills": "Laravel 11, <PERSON><PERSON> 8,Express js, RESTful APIs"}, "contact": {"title": "Get In Touch", "contact_information": "Contact Information", "contact_intro": "I'm always interested in hearing about new projects and opportunities. Feel free to reach out through any of these channels:", "name": "Name", "email": "Email", "message": "Message", "send_message": "Send Message", "sending": "Sending...", "success_message": "Message sent successfully!", "error_message": "Failed to send message."}, "projects": {"title": "Featured Projects", "demo": "Demo", "code": "Code", "project_1_title": "E-Commerce Platform", "project_1_description": "A full-featured e-commerce platform built with Laravel , featuring real-time inventory management and secure payment processing.", "project_2_title": "The Auction Platform for Agricultural Equipment", "project_2_description": "AgriBid is the auction platform for agricultural equipment, allowing professionals in the farming industry to buy and sell quality machinery at competitive prices. With our simple and secure interface, participate in auctions with confidence and find the tools you need to boost your production.", "project_3_title": "Clothing E-Commerce Store", "project_3_description": "A full-stack e-commerce platform for selling men's, women's, and kids' clothing. Built with Express.js for the backend, React.js for the frontend, MongoDB for the database, and Cloudinary for image storage. The project includes separate interfaces for users and administrators."}, "skills": {"title": "Skills & Expertise", "category_1_title": "Frontend Development", "category_2_title": "Backend Development", "category_3_title": "Database", "category_4_title": "Programming Languages", "skill_1_1": "React js", "skill_1_2": "Tailwind CSS", "skill_1_3": "Next.js", "skill_2_1": "Laravel 11", "skill_2_2": "PHP 8", "skill_2_3": "RESTful APIs", "skill_3_1": "MySQL", "skill_4_1": "PHP", "skill_4_2": "JavaScript", "skill_4_3": "Python", "skill_4_4": "Dart"}, "nav": {"home": "Home", "about": "About", "services": "Services", "projects": "Projects", "skills": "Skills", "experience": "Experience", "contact": "Contact"}, "experience": {"title": "Experience & Education", "subtitle": "My professional journey and educational background", "achievements": "Key Achievements", "download_cv": "Download CV", "item_1_title": "Full Stack Developer", "item_1_company": "Tech Solutions", "item_1_location": "Remote", "item_1_period": "2023 - Present", "item_1_description": "Developing modern web applications using React, Laravel, and cloud technologies.", "item_1_achievement_1": "Built 5+ production applications", "item_1_achievement_2": "Improved performance by 40%", "item_1_achievement_3": "Led a team of 3 developers", "item_2_title": "Web Developer", "item_2_company": "Digital Agency", "item_2_location": "<PERSON><PERSON><PERSON>", "item_2_period": "2022 - 2023", "item_2_description": "Created responsive websites and e-commerce platforms for various clients.", "item_2_achievement_1": "Delivered 10+ client projects", "item_2_achievement_2": "Increased client satisfaction by 95%", "item_2_achievement_3": "Specialized in Laravel & React", "item_3_title": "Master in Computer Science", "item_3_company": "University of Bordj Bou Arreridj", "item_3_location": "<PERSON><PERSON><PERSON>, Algeria", "item_3_period": "2021 - 2023", "item_3_description": "Specialized in Software Engineering and Web Development.", "item_3_achievement_1": "Graduated with Honors", "item_3_achievement_2": "Thesis on Modern Web Technologies", "item_3_achievement_3": "Active in coding competitions", "item_4_title": "Bachelor in Computer Science", "item_4_company": "University of Bordj Bou Arreridj", "item_4_location": "<PERSON><PERSON><PERSON>, Algeria", "item_4_period": "2018 - 2021", "item_4_description": "Foundation in computer science, programming, and software development.", "item_4_achievement_1": "Strong academic performance", "item_4_achievement_2": "Participated in hackathons", "item_4_achievement_3": "Built first web applications"}, "services": {"title": "Services I Offer", "subtitle": "Comprehensive development services to bring your ideas to life", "cta_title": "Ready to Start Your Project?", "cta_description": "Let's discuss your ideas and create something amazing together.", "get_quote": "Get a Quote", "view_portfolio": "View Portfolio", "service_1_title": "Web Development", "service_1_description": "Custom web applications built with modern technologies like React, Laravel, and Node.js.", "service_1_feature_1": "Responsive Design", "service_1_feature_2": "SEO Optimized", "service_1_feature_3": "Fast Performance", "service_1_feature_4": "Cross-browser Compatible", "service_2_title": "Mobile Development", "service_2_description": "Native and cross-platform mobile applications for iOS and Android.", "service_2_feature_1": "React Native", "service_2_feature_2": "Flutter", "service_2_feature_3": "Native Performance", "service_2_feature_4": "App Store Ready", "service_3_title": "Backend Development", "service_3_description": "Robust server-side solutions with APIs, databases, and cloud integration.", "service_3_feature_1": "RESTful APIs", "service_3_feature_2": "Database Design", "service_3_feature_3": "Cloud Deployment", "service_3_feature_4": "Security", "service_4_title": "Full-Stack Solutions", "service_4_description": "Complete end-to-end development from concept to deployment.", "service_4_feature_1": "Frontend + Backend", "service_4_feature_2": "Database Integration", "service_4_feature_3": "DevOps", "service_4_feature_4": "Maintenance", "service_5_title": "Performance Optimization", "service_5_description": "Speed up your existing applications and improve user experience.", "service_5_feature_1": "Code Optimization", "service_5_feature_2": "Database Tuning", "service_5_feature_3": "Caching", "service_5_feature_4": "Monitoring", "service_6_title": "Security & Maintenance", "service_6_description": "Keep your applications secure and up-to-date with regular maintenance.", "service_6_feature_1": "Security Audits", "service_6_feature_2": "Updates", "service_6_feature_3": "Backup Solutions", "service_6_feature_4": "24/7 Support"}, "statistics": {"title": "Numbers That Matter", "subtitle": "Some key metrics that showcase my journey and achievements", "commitment": "Committed to excellence in every project", "stat_1_title": "Projects Completed", "stat_1_description": "Successful projects delivered", "stat_2_title": "Happy Clients", "stat_2_description": "Satisfied customers worldwide", "stat_3_title": "Cups of Coffee", "stat_3_description": "Fuel for coding sessions", "stat_4_title": "Years Experience", "stat_4_description": "In web development"}, "footer": {"description": "Passionate web developer creating modern, scalable applications with cutting-edge technologies. Let's build something amazing together.", "location": "<PERSON><PERSON><PERSON>, Algeria", "phone": "+213 783 96 23 48", "quick_links": "Quick Links", "services_title": "Services", "service_1": "Web Development", "service_2": "Mobile Development", "service_3": "Backend Development", "service_4": "Consulting", "made_with": "Made with", "in_algeria": "in Algeria", "back_to_top": "Back to top"}}