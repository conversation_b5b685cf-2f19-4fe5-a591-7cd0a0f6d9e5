@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', system-ui, sans-serif;
    font-weight: 600;
    line-height: 1.2;
  }
}

@layer components {
  /* Modern Professional Buttons */
  .btn-primary {
    @apply inline-flex items-center px-8 py-4 text-sm font-semibold rounded-xl text-white bg-gradient-to-r from-accent-600 to-accent-700 hover:from-accent-700 hover:to-accent-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-500 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-accent-500/25 transform hover:-translate-y-0.5;
  }

  .btn-secondary {
    @apply inline-flex items-center px-8 py-4 text-sm font-semibold rounded-xl text-primary-700 dark:text-primary-300 bg-white dark:bg-primary-800/50 border border-primary-200 dark:border-primary-700 hover:bg-primary-50 dark:hover:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-500 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
  }

  .btn-ghost {
    @apply inline-flex items-center px-6 py-3 text-sm font-medium rounded-lg text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-500 transition-all duration-200;
  }

  /* Modern Cards */
  .card {
    @apply bg-white dark:bg-primary-800/50 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 border border-primary-100 dark:border-primary-700/50 backdrop-blur-sm;
  }

  .card-hover {
    @apply hover:shadow-xl hover:shadow-accent-500/10 dark:hover:shadow-accent-400/10 hover:-translate-y-1;
  }

  .card-interactive {
    @apply cursor-pointer hover:scale-[1.02] active:scale-[0.98] transition-transform duration-200;
  }

  /* Glass Morphism */
  .glass {
    @apply bg-white/70 dark:bg-primary-900/70 backdrop-blur-xl border border-white/20 dark:border-primary-700/30 shadow-lg;
  }

  .glass-strong {
    @apply bg-white/90 dark:bg-primary-900/90 backdrop-blur-2xl border border-white/30 dark:border-primary-700/40;
  }

  /* Typography */
  .gradient-text {
    @apply bg-gradient-to-r from-accent-600 to-secondary-600 bg-clip-text text-transparent;
  }

  .text-balance {
    text-wrap: balance;
  }

  /* Layout */
  .section-padding {
    @apply py-24 lg:py-32;
  }

  .section-padding-sm {
    @apply py-16 lg:py-20;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-6 sm:px-8 lg:px-12;
  }

  /* Professional spacing system */
  .space-y-section > * + * {
    @apply mt-24 lg:mt-32;
  }

  /* Modern focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-500 focus:ring-offset-white dark:focus:ring-offset-primary-900;
  }

  /* Professional grid pattern */
  .bg-grid-pattern {
    background-image: radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.15) 1px, transparent 0);
    background-size: 20px 20px;
  }

  /* Modern scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(148 163 184) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(148 163 184);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(100 116 139);
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-400 {
    animation-delay: 400ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }

  /* 3D Transform utilities */
  .perspective-1000 {
    perspective: 1000px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }

  .transform-gpu {
    transform: translate3d(0, 0, 0);
  }

  .hover\:rotateY-5:hover {
    transform: rotateY(5deg);
  }

  .translateZ-4 {
    transform: translateZ(4px);
  }

  /* Line clamp utility */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Advanced 3D Effects */
  .card-3d {
    transform-style: preserve-3d;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
  }

  .card-3d:hover {
    transform: translateY(-20px) rotateX(15deg) rotateY(5deg) scale(1.05);
    box-shadow:
      0 40px 80px rgba(0, 0, 0, 0.2),
      0 0 40px rgba(59, 130, 246, 0.3),
      inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  .card-face {
    backface-visibility: hidden;
    transform-style: preserve-3d;
  }

  .card-back {
    transform: rotateY(180deg);
  }

  /* Holographic effect */
  .holographic {
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 70%
    );
    background-size: 200% 200%;
    animation: holographic 3s ease-in-out infinite;
  }

  @keyframes holographic {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* Floating particles */
  .particle {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
    animation: float 6s ease-in-out infinite;
  }

  .particle:nth-child(1) { animation-delay: 0s; }
  .particle:nth-child(2) { animation-delay: 1s; }
  .particle:nth-child(3) { animation-delay: 2s; }
  .particle:nth-child(4) { animation-delay: 3s; }
  .particle:nth-child(5) { animation-delay: 4s; }

  /* Glass morphism enhanced */
  .glass-enhanced {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  /* Neon glow effect */
  .neon-glow {
    box-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 20px currentColor,
      0 0 40px currentColor;
  }

  /* 3D Text effect */
  .text-3d {
    text-shadow:
      1px 1px 0 #ccc,
      2px 2px 0 #c9c9c9,
      3px 3px 0 #bbb,
      4px 4px 0 #b9b9b9,
      5px 5px 0 #aaa,
      6px 6px 1px rgba(0,0,0,.1),
      0 0 5px rgba(0,0,0,.1),
      1px 1px 3px rgba(0,0,0,.3),
      3px 3px 5px rgba(0,0,0,.2),
      5px 5px 10px rgba(0,0,0,.25);
  }

  /* Perspective container */
  .perspective-container {
    perspective: 2000px;
    perspective-origin: center center;
  }

  /* Enhanced transform utilities */
  .transform-3d {
    transform-style: preserve-3d;
    will-change: transform;
  }

  .rotate-x-15 {
    transform: rotateX(15deg);
  }

  .rotate-y-15 {
    transform: rotateY(15deg);
  }

  .rotate-z-15 {
    transform: rotateZ(15deg);
  }

  /* Responsive 3D adjustments */
  @media (max-width: 768px) {
    .perspective-container {
      perspective: 800px;
    }

    .card-3d:hover {
      transform: translateY(-10px) rotateX(8deg) rotateY(3deg) scale(1.02);
    }

    .transform-3d {
      transform-style: flat;
    }
  }

  @media (max-width: 475px) {
    .perspective-container {
      perspective: 600px;
    }

    .card-3d:hover {
      transform: translateY(-5px) scale(1.01);
    }

    .holographic {
      animation: none;
    }

    .particle {
      display: none;
    }
  }

  /* Touch device optimizations */
  @media (hover: none) and (pointer: coarse) {
    .card-3d:hover {
      transform: none;
    }

    .card-3d:active {
      transform: scale(0.98);
    }
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .card-3d,
    .holographic,
    .particle,
    .animate-float,
    .animate-pulse,
    .animate-glow-pulse {
      animation: none !important;
      transition: none !important;
    }

    .card-3d:hover {
      transform: scale(1.02);
    }
  }
}

html[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

html[dir="ltr"] {
  direction: ltr;
  text-align: left;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-gray-500;
}

/* Selection styling */
::selection {
  @apply bg-primary-500 text-white;
}

::-moz-selection {
  @apply bg-primary-500 text-white;
}
