import { useEffect } from 'react';
import { Github, ExternalLink } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const projects = [
  {
    title: "E-commerce Platform",
    description: "A modern e-commerce platform built with <PERSON><PERSON> and <PERSON>vel",
    image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    technologies: ["React", "Laravel", "MySQL", "Tailwind CSS"],
    github: "https://github.com/MohammedBetkaoui",
    demo: "https://example.com"
  },
  {
    title: "Task Management App",
    description: "A collaborative task management application with real-time updates",
    image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    technologies: ["React", "Node.js", "MongoDB", "Socket.io"],
    github: "https://github.com/MohammedBetkaoui",
    demo: "https://example.com"
  },
  {
    title: "Portfolio Website",
    description: "A responsive portfolio website with modern design and animations",
    image: "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    technologies: ["React", "TypeScript", "Tailwind CSS", "Vite"],
    github: "https://github.com/MohammedBetkaoui",
    demo: "https://example.com"
  }
];

const Projects = () => {
  const { t, i18n } = useTranslation();

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') || 'en';
    i18n.changeLanguage(savedLanguage);
    document.documentElement.dir = savedLanguage === 'ar' ? 'rtl' : 'ltr';
  }, [i18n]);

  return (
    <section id="projects" className="section-padding bg-primary-50/30 dark:bg-primary-900/30 relative overflow-hidden">
      {/* Clean Background */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-accent-100 dark:bg-accent-900/20 rounded-full blur-3xl opacity-30"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-secondary-100 dark:bg-secondary-900/20 rounded-full blur-3xl opacity-20"></div>
      </div>

      <div className="container-custom relative z-10">
        {/* Modern Header */}
        <div className="text-center mb-20">
          <h2 className="text-3xl lg:text-4xl font-display font-bold text-primary-900 dark:text-white mb-4">
            {t('projects.title')}
          </h2>
          <div className="w-16 h-1 bg-gradient-to-r from-accent-500 to-secondary-500 mx-auto rounded-full mb-6"></div>
          <p className="text-lg text-primary-600 dark:text-primary-400 max-w-2xl mx-auto text-balance">
            {t('projects.subtitle', { defaultValue: 'Découvrez mes projets les plus récents.' })}
          </p>
        </div>

        {/* Modern Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <div key={index} className="card card-hover card-interactive group p-0 overflow-hidden">
              {/* Project Image */}
              <div className="relative h-48 overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-primary-900/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                {/* Project Links Overlay */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <div className="flex space-x-3">
                    {project.github && (
                      <a
                        href={project.github}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-3 bg-white/90 dark:bg-primary-800/90 rounded-full hover:bg-white dark:hover:bg-primary-700 transition-colors duration-200 shadow-lg"
                      >
                        <Github className="w-5 h-5 text-primary-700 dark:text-primary-300" />
                      </a>
                    )}
                    {project.demo && (
                      <a
                        href={project.demo}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-3 bg-accent-500 hover:bg-accent-600 rounded-full transition-colors duration-200 shadow-lg"
                      >
                        <ExternalLink className="w-5 h-5 text-white" />
                      </a>
                    )}
                  </div>
                </div>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <h3 className="text-xl font-display font-bold text-primary-900 dark:text-white mb-3 group-hover:text-accent-600 dark:group-hover:text-accent-400 transition-colors duration-200">
                  {t(`projects.project_${index + 1}_title`, { defaultValue: project.title })}
                </h3>
                <p className="text-primary-600 dark:text-primary-400 mb-4 text-sm leading-relaxed">
                  {t(`projects.project_${index + 1}_description`, { defaultValue: project.description })}
                </p>
                
                {/* Tech Stack */}
                <div className="flex flex-wrap gap-2">
                  {project.technologies.map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-primary-100 dark:bg-primary-800 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-700"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View All Projects Button */}
        <div className="text-center mt-16">
          <a
            href="https://github.com/MohammedBetkaoui"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-secondary group"
          >
            <span>{t('projects.view_all', { defaultValue: 'Voir tous les projets' })}</span>
            <ExternalLink className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
          </a>
        </div>
      </div>
    </section>
  );
};

export default Projects;
