{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "framer-motion": "^11.17.0", "i18next": "^24.2.0", "i18next-browser-languagedetector": "^8.0.2", "i18next-http-backend": "^3.0.1", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.4.0", "react-intersection-observer": "^9.14.1", "react-router-dom": "^6.22.2", "react-select": "^5.9.0"}, "devDependencies": {"@eslint/js": "^8.56.0", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^13.24.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "typescript-eslint": "^7.0.1", "vite": "^5.4.11"}}